{"name": "api-gateway-service", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "backend/services/api-gateway/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"webpackConfig": "backend/services/api-gateway/webpack.config.js", "outputPath": "dist/backend/services/api-gateway", "main": "backend/services/api-gateway/src/main.ts", "tsConfig": "backend/services/api-gateway/tsconfig.app.json"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "api-gateway-service:build"}, "configurations": {"production": {"buildTarget": "api-gateway-service:build:production"}}}, "serve-static": {"executor": "@nx/js:node", "options": {"buildTarget": "api-gateway-service:build", "watch": false}}}}