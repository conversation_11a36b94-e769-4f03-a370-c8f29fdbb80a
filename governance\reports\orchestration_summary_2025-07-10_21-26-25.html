<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Meqenet.et Governance Orchestration Summary</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      h1,
      h2,
      h3 {
        color: #333;
      }
      .dashboard-container {
        max-width: 1200px;
        margin: 0 auto;
      }
      .header {
        background-color: #2c3e50;
        color: white;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      .header h1 {
        margin: 0;
      }
      .header p {
        margin: 5px 0 0;
        opacity: 0.8;
      }
      .summary-stats {
        display: flex;
        justify-content: space-around;
        background-color: white;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 20px;
      }
      .stat-box {
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
      }
      .stat-label {
        color: #777;
      }
      .chart-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 20px;
      }
      .chart-container {
        background-color: white;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 15px;
        flex: 1;
        min-width: 45%;
      }
      .chart-container h3 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
      }
      iframe {
        border: none;
        width: 100%;
        height: 400px;
      }
      .footer {
        text-align: center;
        margin-top: 30px;
        color: #777;
        font-size: 0.9em;
      }
    </style>
  </head>
  <body>
    <div class="dashboard-container">
      <div class="header">
        <h1>🎯 Meqenet.et Governance Orchestration Summary</h1>
        <p>Generated: 2025-07-10 21:26:25 UTC</p>
      </div>

      <div class="summary-stats">
        <div class="stat-box">
          <div class="stat-number" style="color: green">2</div>
          <div class="stat-label">Successful</div>
        </div>
        <div class="stat-box">
          <div class="stat-number" style="color: red">4</div>
          <div class="stat-label">Failed</div>
        </div>
        <div class="stat-box">
          <div class="stat-number" style="color: blue">6</div>
          <div class="stat-label">Total Dashboards</div>
        </div>
        <div class="stat-box">
          <div class="stat-number" style="color: purple">14.3s</div>
          <div class="stat-label">Total Duration</div>
        </div>
      </div>

      <div class="chart-row">
        <div class="chart-container">
          <h3>Complete Orchestration Dashboard</h3>
          <iframe src="orchestration_dashboard.html"></iframe>
        </div>
      </div>

      <div class="footer">
        <p>
          Governance Orchestration Dashboard v2.0 with Interactive
          Visualizations | Next execution: Scheduled
        </p>
      </div>
    </div>
  </body>
</html>
