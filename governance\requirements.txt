# Meqenet.et Governance Framework Dependencies
# Install with: pip install -r requirements.txt

# Core Dependencies (Required)
pyyaml>=6.0
schedule>=1.2.0
aiohttp>=3.8.0
pandas>=1.5.0
numpy>=1.21.0
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
requests>=2.28.0
yfinance>=0.2.22
textblob>=0.17.1
packaging>=21.0
importlib_metadata>=4.0.0; python_version<"3.8"

# Optional Dependencies for Enhanced Features
# Uncomment lines below to install optional packages

# Machine Learning & AI
# scikit-learn>=1.1.0
# tensorflow>=2.10.0

# Cloud & Infrastructure
# boto3>=1.26.0
# psutil>=5.9.0

# Database Connectors
# psycopg2-binary>=2.9.0
# redis>=4.3.0

# Web & Visualization
# flask>=2.2.0
# dash>=2.7.0
# streamlit>=1.15.0

# Background Processing
# celery>=5.2.0 