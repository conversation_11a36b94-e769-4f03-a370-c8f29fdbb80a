# Ignore node_modules, as they should be installed within the container
**/node_modules

# Ignore build outputs and caches
dist
coverage
.nx/cache
tmp-*

# Ignore IDE and OS-specific files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Ignore local development artifacts
.env
*.local
.venv

# Ignore logs and temporary files
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Ignore Git
.git
.gitignore

# Ignore local CI validation reports
governance/logs
governance/reports

# Ignore security audit temporary directories
.pip-audit-deps*

# Ignore system files
Thumbs.db
.DS_Store 