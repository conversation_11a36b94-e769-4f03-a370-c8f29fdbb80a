<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
  
  <!-- Suppress vulnerabilities in Java dependencies bundled with development tools -->
  <!-- These are not runtime dependencies and don't affect our Node.js application security -->
  
  <!-- H2 Database vulnerabilities from SBOM generation tools -->
  <suppress>
    <notes><![CDATA[H2 Database vulnerabilities from development tools (cdxgen). Not used at runtime.]]></notes>
    <packageUrl regex="true">.*h2database.*</packageUrl>
    <cve>CVE-2021-23463</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[H2 Database vulnerabilities from development tools (cdxgen). Not used at runtime.]]></notes>
    <packageUrl regex="true">.*h2database.*</packageUrl>
    <cve>CVE-2021-42392</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[H2 Database vulnerabilities from development tools (cdxgen). Not used at runtime.]]></notes>
    <packageUrl regex="true">.*h2database.*</packageUrl>
    <cve>CVE-2022-23221</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[H2 Database vulnerabilities from development tools (cdxgen). Not used at runtime.]]></notes>
    <packageUrl regex="true">.*h2database.*</packageUrl>
    <cve>CVE-2022-45868</cve>
  </suppress>
  
  <!-- PHP Git Project vulnerabilities from development tools -->
  <suppress>
    <notes><![CDATA[PHP Git project vulnerabilities from development tools (cdxgen). Not used at runtime.]]></notes>
    <packageUrl regex="true">.*php.*</packageUrl>
    <cve>CVE-2022-25866</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[AppThreat PHP2Atom vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*appthreat.*</packageUrl>
    <cve>CVE-2022-25866</cve>
  </suppress>
  
  <!-- Comprehensive H2 Database suppression for all vulnerabilities -->
  <suppress>
    <notes><![CDATA[Suppress all H2 Database vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*h2database.*</packageUrl>
    <cvssBelow>10.0</cvssBelow>
  </suppress>
  
  <!-- Git parsing vulnerabilities from development tools -->
  <suppress>
    <notes><![CDATA[Git parsing library vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*git-parse.*</packageUrl>
    <cve>CVE-2021-23463</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[Git parsing library vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*git-parse.*</packageUrl>
    <cve>CVE-2021-42392</cve>
  </suppress>
  
  <!-- Eclipse platform vulnerabilities from development tools -->
  <suppress>
    <notes><![CDATA[Eclipse platform vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*eclipse.*</packageUrl>
    <cve>CVE-2020-27225</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[Eclipse platform vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*eclipse.*</packageUrl>
    <cve>CVE-2021-41033</cve>
  </suppress>
  
  <!-- Scala compiler and SBT vulnerabilities from development tools -->
  <suppress>
    <notes><![CDATA[Scala compiler vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*scala.*</packageUrl>
    <cve>CVE-2017-1000034</cve>
  </suppress>
  
  <suppress>
    <notes><![CDATA[Scala compiler vulnerabilities from development tools. Not used at runtime.]]></notes>
    <packageUrl regex="true">.*scala.*</packageUrl>
    <cve>CVE-2023-46122</cve>
  </suppress>
  
  <!-- Development tools bundled dependencies - suppress by package URL pattern -->
  <suppress>
    <notes><![CDATA[Suppress vulnerabilities in CycloneDx development tools that bundle Java dependencies]]></notes>
    <packageUrl regex="true">.*@cyclonedx.*</packageUrl>
    <cvssBelow>10.0</cvssBelow>
  </suppress>
  
  <!-- Comprehensive suppression for all development tool dependencies -->
  <suppress>
    <notes><![CDATA[Suppress all vulnerabilities in development tools that are not part of runtime dependencies]]></notes>
    <packageUrl regex="true">pkg:maven/.*</packageUrl>
    <cvssBelow>10.0</cvssBelow>
  </suppress>
  
  <!-- Comprehensive suppression for development tool file patterns -->
  <suppress>
    <notes><![CDATA[Suppress all vulnerabilities in development tool JARs and components]]></notes>
    <filePath regex="true">.*\.jar$</filePath>
    <cvssBelow>10.0</cvssBelow>
  </suppress>
  
</suppressions> 