{"name": "@meqenet/root", "version": "1.0.0", "description": "Meqenet.et - Ethiopia's Leading BNPL Financial Super-App", "private": true, "scripts": {"prepare": "husky", "lint": "pnpm -r --if-present lint", "lint:fix": "eslint . --fix", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:integration": "vitest run integration", "test:e2e": "pnpm nx e2e backend-e2e", "build": "pnpm -r --if-present build", "security:audit": "pnpm audit --audit-level moderate", "security:audit-ci": "audit-ci --config .audit-ci.json", "security:sbom": "cdxgen -o bom.json --include-formulation --include-crypto . && echo '✅ SBOM generated successfully at bom.json'", "security:secrets": "echo 'Note: Install trufflehog separately for secrets scanning'", "security:outdated": "pnpm outdated", "validate:ethiopian-compliance": "echo 'Running Ethiopian FinTech compliance checks...' && pnpm run security:audit", "clean": "pnpm -r --if-present clean", "reset": "pnpm run clean && rm -rf node_modules pnpm-lock.yaml && pnpm install", "start:dev": "cross-env COMPOSE_BAKE=true docker-compose up --build", "start:dev:win": "docker-compose up --build", "start:prod": "cross-env COMPOSE_BAKE=true docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build", "docker:build": "cross-env COMPOSE_BAKE=true docker-compose build", "docker:build:win": "docker-compose build", "docker:build:nocache": "cross-env COMPOSE_BAKE=true docker-compose build --no-cache", "docker:build:nocache:win": "docker-compose build --no-cache", "docker:up": "cross-env COMPOSE_BAKE=true docker-compose up", "docker:up:win": "docker-compose up", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "security:scan-python": "node -e \"const fs = require('fs'); const p = './.pip-audit-deps'; if (fs.existsSync(p)) fs.rmSync(p, { recursive: true, force: true }); fs.mkdirSync(p, { recursive: true });\" && pip install --target=./.pip-audit-deps -r tools/git/requirements.txt > nul && pip-audit --path ./.pip-audit-deps --strict", "security:sbom:validate": "cdxgen -o bom.json --include-formulation --include-crypto --validate . && echo '✅ SBOM validated successfully'"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.633.0", "@grpc/grpc-js": "^1.11.1", "@grpc/proto-loader": "^0.7.15", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.0.0", "@nestjs/websockets": "^11.1.3", "@opentelemetry/api": "1.9.0", "@opentelemetry/exporter-jaeger": "^1.25.1", "@opentelemetry/instrumentation-express": "^0.40.1", "@opentelemetry/instrumentation-http": "^0.52.1", "@opentelemetry/sdk-node": "^0.52.1", "@opentelemetry/semantic-conventions": "^1.25.1", "@prisma/client": "^5.18.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.8", "argon2": "^0.41.1", "bufferutil": "^4.0.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "compression": "^1.7.4", "dotenv": "^17.0.1", "helmet": "^7.1.0", "http-proxy-middleware": "^3.0.0", "i18next": "^23.13.0", "ioredis": "^5.6.1", "nest-winston": "^1.9.7", "nestjs-i18n": "^10.4.5", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "utf-8-validate": "^6.0.5", "uuid": "^10.0.0", "winston": "^3.13.1", "zod": "^3.23.8"}, "devDependencies": {"@cyclonedx/cdxgen": "^11.4.1", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@nx/eslint": "^21.2.3", "@nx/js": "21.2.3", "@nx/nest": "^21.2.3", "@nx/node": "^21.2.3", "@nx/vite": "^21.2.3", "@nx/web": "21.2.3", "@nx/webpack": "21.2.3", "@nx/workspace": "^21.2.3", "@opentelemetry/api": "1.9.0", "@swc-node/register": "~1.10.10", "@swc/core": "~1.12.11", "@swc/helpers": "~0.5.17", "@types/amqplib": "^0.10.5", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/node": "~24.0.12", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "audit-ci": "^7.1.0", "axios": "^1.10.0", "chokidar": "^3.5.3", "cross-env": "^7.0.3", "esbuild": "0.25.0", "eslint": "^9.30.1", "eslint-import-resolver-oxc": "^0.15.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-security": "^3.0.1", "globals": "^16.3.0", "happy-dom": "^18.0.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "npm-check-updates": "^18.0.1", "nx": "^21.2.3", "prettier": "^3.6.2", "prisma": "^5.18.0", "source-map-support": "^0.5.21", "sqlite3": "^5.1.7", "supertest": "7.1.3", "swagger-ui-express": "^5.0.1", "ts-loader": "^9.5.1", "ts-node": "10.9.2", "ts-proto": "^1.178.0", "tsconfig-paths": "^4.2.0", "tslib": "^2.8.1", "typescript": "~5.8.3", "vite": "^6.0.0", "vitest": "^3.2.4", "webpack-cli": "^5.1.4"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md,yml,yaml}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "repository": {"type": "git", "url": "https://github.com/meqenet-et/meqenet.git"}, "keywords": ["fintech", "bnpl", "ethiopia", "payments", "microservices", "telebirr", "fayda-id", "nbe-compliant"], "author": "Meqenet.et Financial Services", "license": "PROPRIETARY", "engines": {"node": ">=18.0.0", "pnpm": ">=10.0.0"}, "volta": {"node": "22.17.0", "pnpm": "10.12.3"}, "packageManager": "pnpm@10.12.3", "pnpm": {"onlyBuiltDependencies": ["@appthreat/sqlite3", "@nestjs/core", "@parcel/watcher", "@prisma/client", "@prisma/engines", "@scarf/scarf", "@swc/core", "argon2", "bcrypt", "grpc-tools", "nx", "prisma", "protobufjs", "sqlite3", "unrs-resolver"], "peerDependencyRules": {"allowedVersions": {"glob": ">=10.0.0", "inflight": ">=2.0.0", "chokidar": "3.x"}}, "packageExtensions": {"test-exclude@6.0.0": {"dependencies": {"glob": "^10.4.5"}}}}}