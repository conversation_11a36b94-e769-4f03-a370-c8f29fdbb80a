{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "outDir": "./dist", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2022", "module": "commonjs", "lib": ["es2022", "dom"], "esModuleInterop": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "strict": true, "types": ["vitest/globals", "node"], "paths": {"@meqenet/auth-service/*": ["backend/services/auth-service/src/*"]}}, "include": ["vitest.d.ts"], "exclude": ["node_modules", "tmp", "templates"]}