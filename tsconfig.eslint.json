{"extends": "./tsconfig.base.json", "compilerOptions": {"allowJs": true, "checkJs": false, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "noEmit": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "**/*.json", "**/*.spec.ts", "**/*.test.ts", "**/*.e2e-spec.ts"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.min.js", "public", ".next", ".cache", "templates"]}