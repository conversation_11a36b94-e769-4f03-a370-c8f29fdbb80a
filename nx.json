{"$schema": "./node_modules/nx/schemas/nx-schema.json", "extends": "nx/presets/npm.json", "affected": {"defaultBase": "main"}, "targetDefaults": {"build": {"cache": true, "inputs": ["production", "^production"]}, "test": {"cache": true, "inputs": ["default", "^default", "{workspaceRoot}/jest.preset.js"]}, "lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.js"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": []}, "generators": {"@nx/nest": {"application": {"linter": "eslint", "unitTestRunner": "jest"}}}, "defaultProject": "api-gateway", "plugins": [{"plugin": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "test"}, "exclude": ["backend-e2e/**/*"]}]}