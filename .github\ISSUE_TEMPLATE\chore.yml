name: 🔧 Chore
description: Technical debt, maintenance, or operational tasks for the Meqenet platform
title: "[CHORE]: "
labels: ["chore", "maintenance"]
assignees: []
body:
  - type: markdown
    attributes:
      value: |
        Use this template for maintenance tasks, technical debt, DevOps work, documentation updates, and other operational activities that don't directly add new features or fix bugs.

  - type: dropdown
    id: chore_type
    attributes:
      label: Chore Type
      description: What type of maintenance task is this?
      options:
        - Technical Debt
        - Documentation Update
        - DevOps/Infrastructure
        - Code Refactoring
        - Dependency Updates
        - Configuration Changes
        - Database Maintenance
        - Security Updates
        - Performance Optimization
        - Testing Infrastructure
        - Monitoring/Logging
        - Compliance/Regulatory
        - Other
    validations:
      required: true

  - type: dropdown
    id: area
    attributes:
      label: Area of Impact
      description: Which area of the system does this chore affect?
      options:
        - Backend Services
        - Frontend Applications
        - Mobile Apps
        - Database
        - Infrastructure/DevOps
        - CI/CD Pipeline
        - Documentation
        - Testing
        - Security
        - Monitoring
        - API Gateway
        - Third-party Integrations
        - Multiple Areas
      multiple: true
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How urgent is this maintenance task?
      options:
        - Critical (Security vulnerability, system instability)
        - High (Blocking development, performance issues)
        - Medium (Should be done soon, minor technical debt)
        - Low (Nice to have, cosmetic improvements)
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Task Description
      description: Describe what needs to be done and why
      placeholder: |
        Clearly describe the maintenance task...

        Example: "Update all Node.js dependencies to latest stable versions to address security vulnerabilities and improve performance."
    validations:
      required: true

  - type: textarea
    id: current_state
    attributes:
      label: Current State
      description: Describe the current situation that needs to be addressed
      placeholder: |
        What is the current state that needs improvement?

        Example: "Several Node.js packages are 6+ months out of date, including security-critical packages like express and jsonwebtoken."

  - type: textarea
    id: desired_state
    attributes:
      label: Desired State
      description: Describe what the system should look like after this chore is completed
      placeholder: |
        What should the system look like after completion?

        Example: "All dependencies updated to latest stable versions, security vulnerabilities resolved, and CI pipeline updated to prevent future dependency drift."
    validations:
      required: true

  - type: textarea
    id: acceptance_criteria
    attributes:
      label: Acceptance Criteria
      description: Define the criteria that must be met for this task to be considered complete
      placeholder: |
        List the acceptance criteria...

        Example:
        - [ ] All npm packages updated to latest stable versions
        - [ ] No security vulnerabilities in npm audit
        - [ ] All tests pass after updates
        - [ ] Documentation updated if APIs changed
        - [ ] CI/CD pipeline validates dependency versions
    validations:
      required: true

  - type: dropdown
    id: risk_level
    attributes:
      label: Risk Level
      description: What is the risk level of this maintenance task?
      options:
        - Low (Routine maintenance, minimal impact)
        - Medium (Some risk of breaking changes)
        - High (Significant changes, thorough testing required)
        - Critical (Major system changes, requires rollback plan)
    validations:
      required: true

  - type: textarea
    id: impact_analysis
    attributes:
      label: Impact Analysis
      description: Analyze the potential impact of this change
      placeholder: |
        Describe the potential impact...

        - What systems/components will be affected?
        - What are the risks?
        - What testing is required?
        - Are there any breaking changes?
        - Does this affect Ethiopian users or merchants directly?

  - type: textarea
    id: tasks
    attributes:
      label: Task Breakdown
      description: Break down the work into specific, actionable tasks
      placeholder: |
        List the specific tasks to be completed...

        Example:
        - [ ] Review current dependency versions
        - [ ] Identify security vulnerabilities
        - [ ] Update dependencies in package.json
        - [ ] Run tests to ensure compatibility
        - [ ] Update documentation
        - [ ] Deploy to staging environment
        - [ ] Validate in production

  - type: dropdown
    id: estimated_effort
    attributes:
      label: Estimated Effort
      description: How much effort do you estimate this will take?
      options:
        - Small (< 1 day)
        - Medium (1-3 days)
        - Large (1-2 weeks)
        - Extra Large (> 2 weeks)
    validations:
      required: true

  - type: textarea
    id: dependencies
    attributes:
      label: Dependencies
      description: Are there any dependencies or prerequisites for this task?
      placeholder: |
        List any dependencies...

        Example:
        - Requires approval from security team
        - Depends on completion of database migration
        - Needs maintenance window for deployment

  - type: dropdown
    id: regulatory_impact
    attributes:
      label: Regulatory Impact
      description: Does this maintenance task have any regulatory implications?
      options:
        - Yes, affects NBE compliance
        - Yes, affects audit trails
        - Yes, affects data retention
        - Yes, affects security compliance
        - No regulatory impact
        - Unsure
    validations:
      required: true

  - type: checkboxes
    id: checklist
    attributes:
      label: Pre-work Checklist
      options:
        - label: I have assessed the risk and impact of this change
          required: true
        - label: I have considered the Ethiopian regulatory requirements
          required: true
        - label: I have identified any dependencies or prerequisites
          required: true
        - label: I have estimated the effort required
          required: true

  - type: textarea
    id: additional_notes
    attributes:
      label: Additional Notes
      description: Any additional information, context, or considerations
      placeholder: |
        Any additional context...

        - Links to relevant documentation
        - Related issues or PRs
        - Special considerations for Ethiopian market
        - Rollback plans if needed
