# Meqenet Monorepo .gitignore

# =============================================================================
# Dependencies & Caches
# =============================================================================
node_modules/
.npm/
.yarn-integrity
.pnp.js
.pnp.loader.mjs
pnpm-lock.yaml
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# =============================================================================
# Build & Compilation Artifacts
# =============================================================================
dist/
build/
out/
coverage/
*.lcov
.nyc_output
*.tsbuildinfo
*.js
*.js.map
!*.config.js
!jest.config.js
!webpack.config.js
!eslint.config.js
!scripts/*.js

# =============================================================================
# Nx & Local Development
# =============================================================================
/.nx
.eslintcache
tmp/
temp/
local/

# =============================================================================
# Environment & Configuration
# =============================================================================
.env
.env.*
!.env.example
*.pem

# =============================================================================
# IDE & Editor-Specific
# =============================================================================
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# =============================================================================
# OS-Specific
# =============================================================================
.DS_Store
Thumbs.db
desktop.ini

# =============================================================================
# Logs
# =============================================================================
logs/
*.log
npm-debug.log*

# =============================================================================
# Test & Reports
# =============================================================================
junit.xml
test-results.xml
jest-stare/

# =============================================================================
# Python
# =============================================================================
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
/venv/
/env/
/.venv/
/.env/
*.egg-info/

# =============================================================================
# Mobile (React Native / Expo)
# =============================================================================
# React Native
/android/app/build/
/android/build/
/android/.gradle/
/ios/build/
/ios/Pods/

# Expo
.expo/
web-build/

# Xcode
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData/
*.hmap
*.ipa
*.xcuserstate

# Android
*.apk
*.ap_
*.dex
*.class
bin/
gen/
build/
local.properties
.idea/
.gradle/
captures/
.externalNativeBuild/
-C/Users/<USER>
Bwoy/Desktop/1/Meqenet/tools/git/git-automation.py
/.venv
-C/Users/<USER>
Bwoy/Desktop/1/Meqenet/tools/git/git-automation.py
venv/
.python-version
.venv/
/venv
-C/Users/<USER>
Bwoy/Desktop/1/Meqenet/tools/git/git-automation.py
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
-C/Users/<USER>
Bwoy/Desktop/1/Meqenet/tools/git/git-automation.py
*.manifest
*.spec
pip-wheel-metadata/
local_settings.py
db.sqlite3
db.sqlite3-journal
instance/
.webassets-cache
.flask_session/
-C/Users/<USER>
Bwoy/Desktop/1/Meqenet/tools/git/git-automation.py
# Scrapy
.scrapy
# Sphinx documentation
docs/_build/
# PyBuilder
target/
# Jupyter Notebook
.ipynb_checkpoints
profile_default/
ipython_config.py
# PEP 582; used by Poetry, Flit, and PDM
__pypackages__/
# Celery
celerybeat-schedule
celerybeat.pid
# SageMath parsed files
*.sage.py
# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
# Spyder project settings
.spyderproject
.spyproject
# Rope project settings
.ropeproject
# Pyre type checker
.pyre/
# pytype static analyzer
.pytype/
# Cython debug symbols
cython_debug/
# MyPy
.mypy_cache/
.dmypy.json
dmypy.json
# Pyre
.pyre/
