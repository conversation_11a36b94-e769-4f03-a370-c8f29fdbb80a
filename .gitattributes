# Set default behavior, in case users don't have core.autocrlf set.
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.html text
*.css text
*.js text
*.json text
*.md text
*.ts text
*.tsx text
*.yaml text
*.yml text
*.sh text

# Declare files that will always have LF line endings.
*.sh eol=lf

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.pdf binary 