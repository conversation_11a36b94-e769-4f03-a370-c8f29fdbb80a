# AI Coder Rules: Secure Development & Quality Standards for Meqenet.et BNPL Ethiopia

## 1. Governance & Architectural Principles

### 1.1 General Coding Practices
- **Readability First:** Prioritize clear, simple, and well-documented code over complex optimizations. Follow established project coding standards (naming, formatting, comments). [[Ref: `08-Architecture.md`]]
- **Linting & Formatting:** Always fix linting errors (ESLint) and adhere to project formatting standards (<PERSON><PERSON><PERSON>) before committing code.
- **Post-Edit Validation:** Always run linting and type checks after code edits (e.g., `npm run lint`, `npm run build`).
- **Project Structure Adherence:** Strictly follow the defined project directory structure outlined in `docs/Stage 1 - Foundation/08-Architecture.md`.
- **Docs-as-Code:** Architectural diagrams must be created using **Mermaid.js** and managed in Git as version-controlled, living documentation. [[Ref: `01-Architecture_Governance.md`]]
- **Completeness:** Ensure code is fully implemented. Leave no TODOs or placeholders.
- **Accuracy:** If unsure about an implementation detail (especially regarding NBE regulations, security, or financial logic), state uncertainty rather than guessing. Consult the relevant documentation, such as `docs/Stage 1 - Foundation/07-Security.md`.

### 1.2 Architectural Foundation: Microservices & Feature-Sliced Design
The Meqenet platform is built on two complementary architectural patterns:
- **Backend**: A **Microservice Architecture** for flexibility, scalability, and resilience.
- **Codebase Organization**: A **Feature-Sliced Architecture (FSA)** is mandatory for both frontend and backend codebases to ensure modularity, clear security boundaries, and maintainability.
[[Ref: `08-Architecture.md`]]

### 1.3 Microservice Governance Principles
All backend services MUST adhere to the following principles:
- **Single Responsibility Principle (SRP)**: Each microservice is responsible for a single business capability.
- **Bounded Context**: Services are designed around a specific business domain.
- **Explicit APIs (API as the Contract)**: All communication MUST occur through well-defined, versioned APIs (gRPC for internal, REST for external). **No direct database sharing.**
- **Decentralized Data Management**: Each microservice owns its database (**Polyglot Persistence**).
- **Independent Deployability**: Each microservice must be independently deployable.
- **Resilience and Fault Isolation**: Services must be designed for failure using patterns like circuit breakers, retries, and timeouts.
[[Ref: `01-Architecture_Governance.md`]]

### 1.4 API Specification & Governance
- **North-South (External APIs)**:
    - **Standard**: **OpenAPI 3.0** is mandatory. The `openapi.yaml` file is the single source of truth.
    - **Development**: Use "spec-first" development.
    - **Versioning**: Use URI-based versioning (e.g., `/v1/payments`). Breaking changes require a new major version.
    - **Lifecycle**: APIs follow a lifecycle: `Design` -> `Active` -> `Deprecated` -> `Retired`.
- **East-West (Internal Service-to-Service)**:
    - **Synchronous**: **gRPC** with Protocol Buffers (`.proto` files) is the preferred standard.
    - **Asynchronous**: Use a centralized **event bus** (e.g., AWS SNS/SQS). Events MUST have a versioned schema, and consumers MUST be idempotent.
[[Ref: `02-API_Specification_and_Governance.md`]]

### 1.5 Feature-Sliced Architecture (FSA) Details
- **Dependency Direction**: Higher layers can only depend on lower layers: `app` → `pages` → `widgets` → `features` → `entities` → `shared`.
- **Isolation**: Features can only import from `shared`, `entities`, or their own internal modules. No cross-feature imports.
- **Public API**: Each feature must expose a clean public API via its `index.ts` file.
- **Feature List**: `auth`, `bnpl`, `marketplace`, `rewards`, `analytics`, `kyc`, `payments`, `credit`, `premium`.

## 2. Security Framework (Zero Trust Model)

Our security is built on a **Zero Trust** model, assuming no implicit trust. [[Ref: `07-Security.md`]]

### 2.1 General Security
- **Input Validation & Sanitization**: Rigorously validate and sanitize ALL inputs (user, API, DB, files, Fayda ID data) using libraries like Zod/Joi.
- **Output Encoding**: Encode all data output to prevent XSS.
- **Secrets Management**: Never hardcode secrets. Use a central vault (**AWS Secrets Manager**). Secrets MUST NOT be in code, config files, or environment variables.
- **Secure Error Handling**: Do not expose sensitive system details or stack traces to users. Log detailed errors securely on the backend.
- **Comprehensive Logging**: Log all security-relevant events for incident response and NBE audits.

### 2.2 Microservice & Communication Security
- **Security Zones**: Services are zoned by criticality (High, Medium, Controlled) with corresponding security requirements.
- **East-West Traffic**: **Mutual TLS (mTLS) is MANDATORY** for all internal service-to-service communication. Unencrypted internal traffic is prohibited.
- **Network Policies**: Use default-deny network policies to explicitly define allowed communication paths between services.

### 2.3 Authentication & Authorization
- **Strong Authentication**: Implement robust MFA (SMS, Email, Biometrics).
- **Authorization**: Enforce the Principle of Least Privilege using RBAC and ABAC.
- **Session Management**: Use secure, unpredictable session tokens (JWTs) with proper expiration and secure storage (Expo SecureStore/Keychain).

### 2.4 Data Protection & Cryptography
- **Data Encryption**: Encrypt sensitive data both in transit (TLS 1.3) and at rest (AES-256-GCM), especially **Fayda National ID data**.
- **Cryptographic Standards**: Use well-vetted, standard cryptographic libraries. Avoid custom cryptography.

## 3. KYC & Identity Verification (Ethiopian Specific)

- **Fayda National ID Only**: Meqenet.et uses **exclusively** the Ethiopian Fayda National ID for identity verification (KYC/eKYC). Do not implement support for any other identity documents.
- **Fayda ID Validation**: Implement robust validation for Fayda ID format and integrate with official verification services.
- **Secure Fayda Data Handling**: Encrypt all Fayda National ID data at rest and in transit. It is classified as **Highly Sensitive Data**.
- **NBE KYC Compliance**: Ensure all identity verification processes comply with NBE KYC and Ethiopian AML regulations.
[[Ref: `04-PRD.md`, `07-Security.md`]]

## 4. Quality & Testing

- **Code Reviews**: Thorough code reviews are mandatory, focusing on logic, security flaws, and standards compliance.
- **Testing**: Write unit, integration, and E2E tests for new code, focusing on critical paths (payments, credit logic, KYC), edge cases, and security-sensitive functionality.
- **Regression Testing**: Ensure changes do not break existing functionality. Automate regression tests where possible.
[[Ref: `docs/Stage 2 -Development/22-Testing_Guidelines.md`]]

## 5. "Paved Road" Technology Stack

Adhere to the recommended "Paved Road" stack. Deviations require a formal review. [[Ref: `09-Tech_Stack.md`]]
- **Backend**: **TypeScript** with **Node.js (LTS)** and **NestJS**.
- **Database**: Polyglot persistence. Default relational is **PostgreSQL** with **Prisma**. Default cache is **Redis**.
- **Frontend (Mobile)**: **React Native** with **TypeScript**.
- **Frontend (Web)**: **Next.js** with **TypeScript** and **Tailwind CSS**.
- **Infrastructure**: **AWS**, containerized with **Docker** and orchestrated with **Kubernetes (EKS/ECS)**.
- **IaC**: **Terraform**.
- **CI/CD**: **GitHub Actions**.
- **Observability**: **Prometheus, Grafana, OpenTelemetry**.
- **Secrets Management**: **AWS Secrets Manager**.

## 6. Project Tooling & Automation (CI/CD)

- **GitHub Actions**: The CI/CD pipeline automatically runs linting, builds, tests, and security audits on pushes and pull requests.
- **Husky Git Hooks**:
  - **Pre-commit**: Runs linting (`lint-staged`) and critical vulnerability scans (`npm audit`).
  - **Pre-push**: Runs tests and high-level vulnerability scans.
- **ESLint**: Enforces code quality, security rules, and FSA dependency constraints.
- **TypeScript**: `strict` mode is enabled. TypeScript compilation errors must be resolved.
- **Dependency Management**: Regularly scan dependencies using `npm audit` or `yarn audit`.
- **Container Security**: All container images MUST be scanned for vulnerabilities in the CI pipeline. Use minimal, hardened base images and run containers as non-root.
[[Ref: `07-Security.md`]]

## 7. Domain-Specific Security

### 7.1 Marketplace & E-commerce
- **Merchant Verification**: Implement robust merchant onboarding and verification.
- **Product Data Security**: Validate and sanitize all merchant-provided product information.
- **Transaction Security**: Ensure secure handling of marketplace transactions.

### 7.2 ML/AI Security & Ethics
- **Model Security**: Protect models from adversarial attacks and data poisoning.
- **Data Privacy**: Ensure ML training data complies with Ethiopian data protection laws.
- **Bias Prevention**: Regularly audit models for bias (especially credit scoring) to ensure fairness.
- **Model Interpretability**: Implement explainable AI for credit decisions.

### 7.3 Rewards & Loyalty Security
- **Fraud Prevention**: Implement robust fraud detection for rewards redemption and point manipulation.
- **Transaction Integrity**: Ensure accurate calculation and attribution of cashback rewards.
- **Partner Integration Security**: Secure all integrations with merchant partners. 