# ---- Base Stage ----
# Use a minimal approach to avoid I/O errors with build tools
FROM node:22-alpine AS base

# Install only essential packages to minimize I/O operations
RUN apk add --no-cache openssl tini && \
    npm install -g pnpm@10.12.3

WORKDIR /app

# ---- Dependencies Stage ----
# Copies only package manifests and installs ALL dependencies (incl. dev)
# This layer is cached and only re-runs if package manifests change.
FROM base AS deps

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml .npmrc ./

# Install dependencies with cache mount for better performance
RUN --mount=type=cache,id=pnpm,target=/pnpm/store \
    pnpm install --frozen-lockfile

# ---- Build Stage ----
# Copies source code and builds the specific application.
FROM deps AS builder

# Copy the rest of the monorepo source (respecting .dockerignore)
COPY . .

# Build the application
RUN pnpm exec nx build auth-service

# Create production deployment
RUN pnpm deploy --filter=@meqenet/auth-service ./deploy --legacy

# ---- Production Stage ----
# Creates the final, small, and secure production image.
FROM base AS production
WORKDIR /app
# Copy the pruned production-ready files from the builder stage's deploy folder
COPY --from=builder /app/deploy .
# Create a non-root user for security
RUN addgroup --system appgroup && adduser --system --ingroup appgroup appuser
USER appuser
# Expose the port the app runs on
EXPOSE 3001
# Use Tini as the entrypoint to properly handle signals
ENTRYPOINT ["/usr/bin/tini", "--"]
# Command to run the application
CMD ["node", "dist/main.js"] 