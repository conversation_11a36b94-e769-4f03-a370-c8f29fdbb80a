# ==================================================================================
# Meqenet.et Authentication Service - Environment Configuration
# Ethiopian Financial Services Compliance Template
# ==================================================================================
# 
# CRITICAL SECURITY NOTICE:
# - Never commit actual credentials to version control
# - Use AWS Secrets Manager or similar for production secrets
# - Ensure all database connections use SSL/TLS encryption
# - This template follows NBE compliance requirements
#
# ==================================================================================

# ==================================================================================
# APPLICATION ENVIRONMENT
# ==================================================================================

# Environment (development, staging, production)
NODE_ENV=development

# Service configuration
APP_NAME=meqenet-auth-service
APP_VERSION=1.0.0
APP_PORT=3001

# Ethiopian timezone
TZ=Africa/Addis_Ababa

# ==================================================================================
# DATABASE CONFIGURATION - PostgreSQL with NBE Compliance
# ==================================================================================

# Primary database connection URL (REQUIRED)
# Format: postgresql://username:password@host:port/database?sslmode=require
# Example for local development:
DATABASE_URL=postgresql://meqenet_user:secure_password_here@localhost:5432/meqenet_auth_dev?sslmode=require&connect_timeout=30

# Connection Pool Settings (Optimized for Ethiopian Infrastructure)
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000
DB_MAX_LIFETIME=1800000

# Database Logging Configuration (NBE Audit Compliance)
DB_LOGGING_ENABLED=true
DB_LOG_LEVEL=info
DB_SLOW_QUERY_THRESHOLD=1000

# Security Settings (Ethiopian Financial Compliance)
DB_ENCRYPTION_AT_REST=true
DB_AUDIT_LOGGING=true
DB_CONNECTION_RETRIES=5
DB_RETRY_DELAY=1000

# Health Check Configuration
DB_HEALTH_CHECK_ENABLED=true
DB_HEALTH_CHECK_INTERVAL=60000
DB_HEALTH_CHECK_TIMEOUT=5000

# ==================================================================================
# AUTHENTICATION & SECURITY
# ==================================================================================

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-for-ethiopian-financial-services
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-key

# Password Security
BCRYPT_ROUNDS=12
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_SPECIAL_CHARS=true

# Session Configuration
SESSION_SECRET=your-session-secret-key-for-audit-compliance
SESSION_TIMEOUT=900000

# Rate Limiting (DDoS Protection for Ethiopian Networks)
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================================================================================
# ETHIOPIAN SPECIFIC CONFIGURATIONS
# ==================================================================================

# Supported Languages
SUPPORTED_LANGUAGES=en,am
DEFAULT_LANGUAGE=en

# Ethiopian Phone Number Validation
PHONE_COUNTRY_CODE=+251
PHONE_VALIDATION_ENABLED=true

# Fayda National ID Configuration
FAYDA_ID_ENCRYPTION_KEY=your-fayda-encryption-key-for-kyc-compliance
FAYDA_ID_VALIDATION_ENABLED=true

# KYC Configuration
KYC_REQUIRED=true
KYC_EXPIRY_MONTHS=12
KYC_DOCUMENT_RETENTION_YEARS=7

# ==================================================================================
# INTEGRATIONS & EXTERNAL SERVICES
# ==================================================================================

# Ethiopian Payment Processors
TELEBIRR_API_URL=https://api.telebirr.et
TELEBIRR_MERCHANT_ID=your-telebirr-merchant-id
TELEBIRR_API_KEY=your-telebirr-api-key

# KYC Service Integration (Example: Didit)
KYC_SERVICE_URL=https://api.didit.com
KYC_SERVICE_API_KEY=your-kyc-service-api-key

# Ethiopian Banking Integration
NBE_REPORTING_ENDPOINT=https://nbe.reporting.endpoint
NBE_API_KEY=your-nbe-api-key

# ==================================================================================
# MONITORING & OBSERVABILITY
# ==================================================================================

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_AUDIT_ENABLED=true
LOG_RETENTION_DAYS=2555

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_ENDPOINT=/health
HEALTH_CHECK_DATABASE=true

# Metrics and Monitoring
METRICS_ENABLED=true
METRICS_ENDPOINT=/metrics
PROMETHEUS_ENABLED=true

# OpenTelemetry Configuration
OTEL_ENABLED=false
OTEL_SERVICE_NAME=meqenet-auth-service
OTEL_EXPORTER_ENDPOINT=http://localhost:4317

# ==================================================================================
# SECURITY & COMPLIANCE
# ==================================================================================

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,https://meqenet.et
CORS_CREDENTIALS=true

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CSP_ENABLED=true

# Data Protection (Ethiopian Privacy Laws)
DATA_RETENTION_ACTIVE_USERS=indefinite
DATA_RETENTION_INACTIVE_USERS=2y
GDPR_COMPLIANCE_ENABLED=false
ETHIOPIAN_DATA_PROTECTION_ENABLED=true

# Audit Trail (NBE Compliance)
AUDIT_LOG_RETENTION_YEARS=7
AUDIT_LOG_ENCRYPTION=true
AUDIT_LOG_IMMUTABLE=true

# Risk Management
RISK_ASSESSMENT_ENABLED=true
FRAUD_DETECTION_ENABLED=true
SUSPICIOUS_ACTIVITY_THRESHOLD=0.7

# ==================================================================================
# DEVELOPMENT & TESTING
# ==================================================================================

# Development Tools
DEBUG_ENABLED=false
SWAGGER_ENABLED=true
SWAGGER_ENDPOINT=/api/docs

# Testing Configuration
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5433/meqenet_auth_test?sslmode=require
TEST_JWT_SECRET=test-jwt-secret-for-development-only
TEST_FAYDA_ENCRYPTION_KEY=test-fayda-key-for-development-only

# ==================================================================================
# ETHIOPIAN REGULATORY COMPLIANCE NOTES
# ==================================================================================
#
# 1. DATABASE_URL must include SSL configuration (sslmode=require)
# 2. All Fayda National ID data must be encrypted using FAYDA_ID_ENCRYPTION_KEY
# 3. Audit logs must be retained for 7 years per NBE requirements
# 4. All financial transactions must be logged with IP address and device info
# 5. KYC documents must be retained and accessible for regulatory audits
# 6. Password policies must meet Ethiopian banking security standards
# 7. Rate limiting protects against attacks on Ethiopian network infrastructure
# 8. All timestamps should be in UTC with Ethiopian timezone conversion
# 9. Multi-language support required for Amharic and English
# 10. Integration endpoints must use mutual TLS for service-to-service communication
#
# ==================================================================================