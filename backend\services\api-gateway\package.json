{"name": "@meqenet/api-gateway", "version": "0.1.0", "description": "API Gateway microservice", "private": true, "license": "UNLICENSED", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^10.2.3", "@types/express": "^5.0.1", "@types/node": "^24.0.12", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "prettier": "^3.4.2", "supertest": "7.1.3", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/microservices": "^11.1.3", "@nestjs/platform-express": "^11.1.3", "http-proxy-middleware": "^3.0.5", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2"}}