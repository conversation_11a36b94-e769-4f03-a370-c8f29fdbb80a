dashboards:
  cco:
    enabled: true
    name: CCO Compliance Dashboard
    schedule_cron: 0 10 * * *
    script_path: governance/dashboards/cco_dashboard.py
    timeout_minutes: 12
  ceo:
    enabled: true
    name: CEO Strategic Dashboard
    schedule_cron: 0 8 * * *
    script_path: governance/dashboards/ceo_dashboard.py
    timeout_minutes: 15
  cfo:
    enabled: true
    name: CFO Financial Dashboard
    schedule_cron: 0 9 * * *
    script_path: governance/dashboards/cfo_dashboard.py
    timeout_minutes: 10
  ciso:
    enabled: true
    name: CISO Security Dashboard
    schedule_cron: "*/15 * * * *"
    script_path: governance/dashboards/ciso_dashboard.py
    timeout_minutes: 8
  cto:
    enabled: true
    name: CTO Technical Dashboard
    schedule_cron: "*/30 * * * *"
    script_path: governance/dashboards/cto_dashboard.py
    timeout_minutes: 10
  unified:
    enabled: true
    name: Unified Governance Dashboard
    schedule_cron: 0 7 * * *
    script_path: governance/dashboards/unified_dashboard.py
    timeout_minutes: 20
platform:
  architecture: 64bit
  in_virtual_env: true
  package_manager: pip
  path_separator: \
  pip_command:
    - C:\Users\<USER>\Desktop\1\Meqenet\.venv\Scripts\python.exe
    - -m
    - pip
  python_executable: C:\Users\<USER>\Desktop\1\Meqenet\.venv\Scripts\python.exe
  shell: cmd
  system: Windows
  version: 10.0.26100
setup_mode: full
setup_timestamp: "2025-07-10 17:42:12.721073"
