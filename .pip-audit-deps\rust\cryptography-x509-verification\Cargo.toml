[package]
name = "cryptography-x509-verification"
version.workspace = true
authors.workspace = true
edition.workspace = true
publish.workspace = true
rust-version.workspace = true

[dependencies]
asn1.workspace = true
cryptography-x509 = { path = "../cryptography-x509" }
cryptography-key-parsing = { path = "../cryptography-key-parsing" }
once_cell = "1"

[dev-dependencies]
pem = { version = "3", default-features = false }
