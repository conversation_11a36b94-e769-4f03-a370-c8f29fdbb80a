{"name": "@meqenet/auth-service", "version": "1.0.0", "description": "Authentication and authorization microservice", "author": "Meqenet Developer <<EMAIL>>", "private": true, "license": "UNLICENSED", "engines": {"node": ">=18.19.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "cross-env NODE_NO_WARNINGS=1 eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration": "jest --config ./test/jest-integration.json", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --force", "proto:generate": "protoc --plugin=protoc-gen-ts_proto=./node_modules/.bin/protoc-gen-ts_proto --ts_proto_out=src/generated --ts_proto_opt=nestJs=true --proto_path=proto proto/*.proto", "security:licenses": "pnpm licenses list --json | node -e 'const fs = require(\"fs\"); const licenses = JSON.parse(fs.readFileSync(0)); const forbidden = [\"GPL\", \"LGPL\", \"AGPL\", \"MS-PL\"]; const forbiddenUsed = Object.keys(licenses).filter(l => forbidden.includes(l)); if (forbiddenUsed.length > 0) { console.error(\"Forbidden licenses found:\", forbiddenUsed); process.exit(1); } const deprecated = [\"boolean\", \"read-package-json\"]; const allDeps = Object.values(licenses).flat(); const deprecatedUsed = allDeps.filter(d => deprecated.includes(d.name)); if (deprecatedUsed.length > 0) { console.error(\"Deprecated packages found:\", deprecatedUsed.map(d=>d.name)); process.exit(1); }'", "docker:build": "docker build -t auth-service .", "docker:run": "docker run -p 3001:3001 auth-service", "security:audit": "pnpm audit --level moderate", "security:check": "snyk test"}, "dependencies": {"@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/swagger": "^11.2.0", "@nestjs/throttler": "^6.4.0", "@prisma/client": "^6.11.1", "argon2": "^0.43.0", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "nest-winston": "^1.10.2", "nestjs-i18n": "^10.5.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^6.11.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "winston": "^3.17.0", "zod": "^4.0.5"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.3", "@types/node": "^24.0.13", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "cross-env": "^7.0.3", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "7.1.3", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}}